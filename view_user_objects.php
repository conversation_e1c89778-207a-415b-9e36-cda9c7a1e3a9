<?php
/**
 * Visualizador de Objetos de Usuario SAP B1
 * 
 * Este script muestra los objetos de usuario definidos (UDOs) de SAP Business One
 * en un formato legible. Los UDOs permiten extender la funcionalidad estándar de SAP
 * creando tablas y formularios personalizados.
 * 
 * Ejemplo de endpoint real: https://servidor-sap:50000/b1s/v2/UserObjectsMD
 * Ejemplo de endpoint para un UDO específico: https://servidor-sap:50000/b1s/v2/EPY_PLPY
 */
header('Content-Type: text/html; charset=utf-8');

$jsonFile = '/Users/<USER>/Sites/php-sapb1-v2/json_responses/user_objects_md.json';

// Verificar si el archivo existe
try {
    if (!file_exists($jsonFile)) {
        throw new Exception("El archivo JSON no se encontró en la ruta especificada.");
    }

    $jsonData = file_get_contents($jsonFile);
    $data = json_decode($jsonData, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Error al decodificar el JSON: " . json_last_error_msg());
    }

    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Visualizador de Objetos de Usuario SAP B1</title>
        <meta name="description" content="Visualización de objetos de usuario personalizados en SAP Business One">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .object { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
            .object-header { 
                background-color: #f5f5f5; 
                padding: 10px; 
                margin-bottom: 10px;
                border-left: 4px solid #0070c0;
            }
            .property { margin-bottom: 5px; }
            .property-name { font-weight: bold; color: #0070c0; }
            .child-table, .find-column { margin-left: 20px; margin-top: 10px; }
            .info-box {
                background-color: #f8f9fa;
                border-left: 4px solid #0070c0;
                padding: 15px;
                margin-bottom: 20px;
            }
            code {
                background-color: #f3f4f5;
                padding: 2px 5px;
                border-radius: 3px;
                font-family: Consolas, monospace;
            }
        </style>
    </head>
    <body>
        <h1>Metadatos de Objetos de Usuario</h1>
        <div class="info-box">
            <h3>¿Qué son estos objetos?</h3>
            <p>Estos son objetos personalizados (UDOs) creados en SAP Business One para extender su funcionalidad.</p>
            <p>Cada objeto puede ser:</p>
            <ul>
                <li><strong>Master Data:</strong> Datos maestros como clientes o productos</li>
                <li><strong>Document:</strong> Documentos como facturas o pedidos</li>
                <li><strong>Transaction:</strong> Transacciones específicas</li>
            </ul>
            <p>Ejemplo de API para consultar estos objetos: <code>https://servidor-sap:50000/b1s/v2/UserObjectsMD</code></p>
        </div>
        <p><strong>Timestamp:</strong> ' . htmlspecialchars($data['metadata']['timestamp']) . '</p>
        <p><strong>Description:</strong> ' . htmlspecialchars($data['metadata']['description']) . '</p>
        <hr>';


    foreach ($data['response']['value'] as $object) {
        echo '<div class="object-header">
                <h2>' . htmlspecialchars($object['Name']) . '</h2>
                <div class="property"><span class="property-name">TableName:</span> ' . htmlspecialchars($object['TableName']) . '</div>
                <div class="property"><span class="property-name">ObjectType:</span> ' . htmlspecialchars($object['ObjectType']) . '</div>
                <div class="property"><span class="property-name">Endpoint:</span> <code>https://servidor-sap:50000/b1s/v2/' . htmlspecialchars($object['Name']) . '</code></div>
            </div>';


        // Mostrar propiedades básicas
        $basicProperties = ['Code', 'LogTableName', 'CanCreateDefaultForm', 'CanDelete', 'CanFind'];
        foreach ($basicProperties as $prop) {
            if (isset($object[$prop])) {
                $value = is_bool($object[$prop]) ? ($object[$prop] ? 'true' : 'false') : $object[$prop];
                echo '<div class="property"><span class="property-name">' . $prop . ':</span> ' . ($value !== null ? htmlspecialchars($value) : 'NULL') . '</div>';
            }
        }

        // Mostrar tablas hijas
        if (!empty($object['UserObjectMD_ChildTables'])) {
            echo '<div class="child-table">
                <h3>Child Tables</h3>';
            foreach ($object['UserObjectMD_ChildTables'] as $child) {
                echo '<div class="property">
                    <div><span class="property-name">TableName:</span> ' . ($child['TableName'] !== null ? htmlspecialchars($child['TableName']) : 'NULL') . '</div>
                    <div><span class="property-name">LogTableName:</span> ' . ($child['LogTableName'] !== null ? htmlspecialchars($child['LogTableName']) : 'NULL') . '</div>
                </div>';
            }
            echo '</div>';
        }

        // Mostrar columnas de búsqueda
        if (!empty($object['UserObjectMD_FindColumns'])) {
            echo '<div class="find-column">
                <h3>Find Columns</h3>';
            foreach ($object['UserObjectMD_FindColumns'] as $column) {
                echo '<div class="property">
                    <div><span class="property-name">Column:</span> ' . ($column['ColumnNumber'] !== null ? htmlspecialchars($column['ColumnNumber']) : 'NULL') . '</div>
                    <div><span class="property-name">Alias:</span> ' . ($column['ColumnAlias'] !== null ? htmlspecialchars($column['ColumnAlias']) : 'NULL') . '</div>
                    <div><span class="property-name">Description:</span> ' . ($column['ColumnDescription'] !== null ? htmlspecialchars($column['ColumnDescription']) : 'NULL') . '</div>
                </div>';
            }
            echo '</div>';
        }

        echo '</div>';
    }

    echo '</body>
    </html>';

} catch (Exception $e) {
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Error</title>
    </head>
    <body>
        <h1>Error</h1>
        <p>' . htmlspecialchars($e->getMessage()) . '</p>
    </body>
    </html>';
}
?>