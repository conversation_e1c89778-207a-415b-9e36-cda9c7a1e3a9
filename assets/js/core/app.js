/**
 * Main Application Controller
 * Handles app initialization, routing, and global state management
 */
window.App = (function() {
    'use strict';

    // Application state
    let state = {
        user: null,
        database: null,
        server: null,
        currentModule: null,
        isAuthenticated: false,
        config: null
    };

    // DOM elements
    let elements = {};

    /**
     * Initialize the application
     */
    function init() {
        console.log('Iniciando Cliente Web SAP Business One...');
        
        // Cache DOM elements
        cacheElements();
        
        // Bind events
        bindEvents();
        
        // Check authentication status
        checkAuthStatus();
        
        // Show login screen by default
        showLogin();
        
        console.log('Aplicación inicializada correctamente');
    }

    /**
     * Cache frequently used DOM elements
     */
    function cacheElements() {
        elements = {
            loginContainer: document.getElementById('login-container'),
            mainApp: document.getElementById('main-app'),
            loginForm: document.getElementById('login-form'),
            loginStatus: document.getElementById('login-status'),
            logoutBtn: document.getElementById('logout-btn'),
            currentUser: document.getElementById('current-user'),
            currentDatabase: document.getElementById('current-database'),
            contentArea: document.getElementById('content-area'),
            navItems: document.querySelectorAll('.nav-item'),
            loadingOverlay: document.getElementById('loading-overlay'),
            quickBtns: document.querySelectorAll('.quick-btn')
        };
    }

    /**
     * Bind event listeners
     */
    function bindEvents() {
        // Login form submission
        if (elements.loginForm) {
            elements.loginForm.addEventListener('submit', handleLogin);
        }

        // Logout button
        if (elements.logoutBtn) {
            elements.logoutBtn.addEventListener('click', handleLogout);
        }

        // Navigation items
        elements.navItems.forEach(item => {
            item.addEventListener('click', function() {
                const module = this.getAttribute('data-module');
                loadModule(module);
            });
        });

        // Quick action buttons
        elements.quickBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.getAttribute('data-action');
                handleQuickAction(action);
            });
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Error de aplicación:', e.error);
            showMessage('Ha ocurrido un error inesperado', 'error');
        });

        // Handle browser back/forward
        window.addEventListener('popstate', function(e) {
            if (e.state && e.state.module) {
                loadModule(e.state.module, false);
            }
        });
    }

    /**
     * Handle login form submission
     */
    async function handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(elements.loginForm);
        const credentials = {
            server: formData.get('server'),
            database: formData.get('database'),
            username: formData.get('username'),
            password: formData.get('password')
        };

        showLoading(true);
        clearLoginStatus();

        try {
            const response = await Auth.login(credentials);
            
            if (response.success) {
                // Update application state
                state.user = response.user;
                state.database = credentials.database;
                state.server = credentials.server;
                state.isAuthenticated = true;

                // Update UI
                updateUserInfo();
                showMainApp();
                showLoginStatus('¡Inicio de sesión exitoso!', 'success');
                
                // Load dashboard
                loadDashboard();
                
                console.log('Usuario conectado exitosamente:', response.user);
            } else {
                showLoginStatus(response.message || 'Error en el inicio de sesión', 'error');
            }
        } catch (error) {
            console.error('Error de conexión:', error);
            showLoginStatus('Error de conexión. Por favor, inténtelo de nuevo.', 'error');
        } finally {
            showLoading(false);
        }
    }

    /**
     * Handle logout
     */
    async function handleLogout() {
        if (confirm('¿Está seguro de que desea cerrar sesión?')) {
            showLoading(true);
            
            try {
                await Auth.logout();
                
                // Clear application state
                state = {
                    user: null,
                    database: null,
                    server: null,
                    currentModule: null,
                    isAuthenticated: false
                };

                // Show login screen
                showLogin();
                clearLoginStatus();
                
                console.log('Sesión cerrada exitosamente');
            } catch (error) {
                console.error('Error al cerrar sesión:', error);
            } finally {
                showLoading(false);
            }
        }
    }

    /**
     * Handle quick action buttons
     */
    function handleQuickAction(action) {
        console.log('Acción rápida:', action);
        
        switch (action) {
            case 'sales-order':
                loadModule('sales', true, { action: 'new', type: 'order' });
                break;
            case 'purchase-order':
                loadModule('purchasing', true, { action: 'new', type: 'order' });
                break;
            case 'item-master':
                loadModule('inventory', true, { action: 'new', type: 'item' });
                break;
            case 'business-partner':
                loadModule('partners', true, { action: 'new', type: 'customer' });
                break;
            default:
                console.warn('Unknown quick action:', action);
        }
    }

    /**
     * Load a module
     */
    function loadModule(moduleName, pushState = true, params = {}) {
        if (!state.isAuthenticated) {
            showLogin();
            return;
        }

        console.log('Loading module:', moduleName, params);
        
        // Update navigation active state
        updateNavigation(moduleName);
        
        // Update current module
        state.currentModule = moduleName;
        
        // Push state for browser history
        if (pushState) {
            history.pushState({ module: moduleName, params }, '', `#${moduleName}`);
        }

        // Show loading
        showLoading(true);

        // Load module content
        try {
            switch (moduleName) {
                case 'sales':
                    if (window.SalesModule) {
                        SalesModule.load(elements.contentArea, params);
                    }
                    break;
                case 'purchasing':
                    if (window.PurchasingModule) {
                        PurchasingModule.load(elements.contentArea, params);
                    }
                    break;
                case 'inventory':
                    if (window.InventoryModule) {
                        InventoryModule.load(elements.contentArea, params);
                    }
                    break;
                case 'production':
                    if (window.ProductionModule) {
                        ProductionModule.load(elements.contentArea, params);
                    }
                    break;
                case 'partners':
                    if (window.PartnersModule) {
                        PartnersModule.load(elements.contentArea, params);
                    }
                    break;
                case 'service':
                    if (window.ServiceModule) {
                        ServiceModule.load(elements.contentArea, params);
                    }
                    break;
                case 'banking':
                    if (window.BankingModule) {
                        BankingModule.load(elements.contentArea, params);
                    }
                    break;
                case 'financials':
                    if (window.FinancialsModule) {
                        FinancialsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'mrp':
                    if (window.MRPModule) {
                        MRPModule.load(elements.contentArea, params);
                    }
                    break;
                case 'crm':
                    if (window.CRMModule) {
                        CRMModule.load(elements.contentArea, params);
                    }
                    break;
                case 'projects':
                    if (window.ProjectsModule) {
                        ProjectsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'hr':
                    if (window.HRModule) {
                        HRModule.load(elements.contentArea, params);
                    }
                    break;
                case 'assets':
                    if (window.AssetsModule) {
                        AssetsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'reports':
                    if (window.ReportsModule) {
                        ReportsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'administration':
                    if (window.AdministrationModule) {
                        AdministrationModule.load(elements.contentArea, params);
                    }
                    break;
                default:
                    console.warn('Unknown module:', moduleName);
                    loadDashboard();
            }
        } catch (error) {
            console.error('Error loading module:', error);
            showMessage('Error al cargar el módulo. Por favor, inténtelo de nuevo.', 'error');
            loadDashboard();
        } finally {
            showLoading(false);
        }
    }

    /**
     * Load dashboard
     */
    function loadDashboard() {
        // Update navigation
        updateNavigation(null);
        
        // Show dashboard content
        const dashboardHTML = `
            <div class="dashboard">
                <h1>Welcome to SAP Business One</h1>
                <div class="dashboard-widgets">
                    <div class="widget">
                        <h3>Quick Actions</h3>
                        <div class="quick-actions">
                            <button class="quick-btn" data-action="sales-order">
                                <i class="fas fa-plus"></i>
                                New Sales Order
                            </button>
                            <button class="quick-btn" data-action="purchase-order">
                                <i class="fas fa-plus"></i>
                                New Purchase Order
                            </button>
                            <button class="quick-btn" data-action="item-master">
                                <i class="fas fa-plus"></i>
                                New Item
                            </button>
                            <button class="quick-btn" data-action="business-partner">
                                <i class="fas fa-plus"></i>
                                New Business Partner
                            </button>
                        </div>
                    </div>
                    
                    <div class="widget">
                        <h3>Recent Documents</h3>
                        <div id="recent-documents">
                            <p>Loading recent documents...</p>
                        </div>
                    </div>
                    
                    <div class="widget">
                        <h3>System Status</h3>
                        <div class="system-status">
                            <p><strong>Server:</strong> ${state.server}</p>
                            <p><strong>Database:</strong> ${state.database}</p>
                            <p><strong>User:</strong> ${state.user ? state.user.username : 'Unknown'}</p>
                            <p><strong>Status:</strong> <span style="color: green;">Connected</span></p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        elements.contentArea.innerHTML = dashboardHTML;
        
        // Re-bind quick action buttons
        const newQuickBtns = elements.contentArea.querySelectorAll('.quick-btn');
        newQuickBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.getAttribute('data-action');
                handleQuickAction(action);
            });
        });
        
        // Load recent documents
        loadRecentDocuments();
        
        // Update URL
        history.pushState({ module: 'dashboard' }, '', '#dashboard');
    }

    /**
     * Load recent documents
     */
    async function loadRecentDocuments() {
        try {
            const response = await API.get('/documents/recent');
            const container = document.getElementById('recent-documents');
            
            if (response.success && response.data.length > 0) {
                const documentsHTML = response.data.map(doc => `
                    <div class="recent-document">
                        <strong>${doc.type}</strong> #${doc.number}
                        <br>
                        <small>${doc.customer} - ${formatDate(doc.date)}</small>
                    </div>
                `).join('');
                
                container.innerHTML = documentsHTML;
            } else {
                container.innerHTML = '<p>No recent documents found.</p>';
            }
        } catch (error) {
            console.error('Error loading recent documents:', error);
            const container = document.getElementById('recent-documents');
            if (container) {
                container.innerHTML = '<p>Error loading recent documents.</p>';
            }
        }
    }

    /**
     * Update navigation active state
     */
    function updateNavigation(activeModule) {
        elements.navItems.forEach(item => {
            const module = item.getAttribute('data-module');
            if (module === activeModule) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Show login screen
     */
    function showLogin() {
        elements.loginContainer.classList.add('active');
        elements.mainApp.classList.remove('active');
        document.title = 'SAP Business One - Login';
    }

    /**
     * Show main application
     */
    function showMainApp() {
        elements.loginContainer.classList.remove('active');
        elements.mainApp.classList.add('active');
        document.title = 'SAP Business One - Web Client';
    }

    /**
     * Update user information in header
     */
    function updateUserInfo() {
        if (state.user) {
            elements.currentUser.textContent = state.user.username;
        }
        
        if (state.database) {
            elements.currentDatabase.textContent = state.database;
            elements.currentDatabase.className = `database-badge ${state.database === 'SBO_ECOM' ? 'prod' : ''}`;
        }
    }

    /**
     * Show/hide loading overlay
     */
    function showLoading(show) {
        if (show) {
            elements.loadingOverlay.classList.add('active');
        } else {
            elements.loadingOverlay.classList.remove('active');
        }
    }

    /**
     * Show login status message
     */
    function showLoginStatus(message, type) {
        elements.loginStatus.textContent = message;
        elements.loginStatus.className = `login-status ${type}`;
    }

    /**
     * Clear login status
     */
    function clearLoginStatus() {
        elements.loginStatus.textContent = '';
        elements.loginStatus.className = 'login-status';
    }

    /**
     * Show general message (could be expanded to show toasts)
     */
    function showMessage(message, type) {
        console.log(`[${type.toUpperCase()}] ${message}`);
        // Could implement toast notifications here
        alert(message);
    }

    /**
     * Check authentication status on app start
     */
    function checkAuthStatus() {
        // Check if user session exists (could check localStorage, sessionStorage, etc.)
        const savedUser = localStorage.getItem('sap_user');
        if (savedUser) {
            try {
                state.user = JSON.parse(savedUser);
                state.isAuthenticated = true;
                // Could validate session with server here
            } catch (error) {
                console.error('Error parsing saved user:', error);
                localStorage.removeItem('sap_user');
            }
        }
    }

    /**
     * Format date helper
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    /**
     * Get current application state
     */
    function getState() {
        return { ...state };
    }

    /**
     * Public API
     */
    return {
        init,
        loadModule,
        loadDashboard,
        showLoading,
        showMessage,
        getState
    };
})();
